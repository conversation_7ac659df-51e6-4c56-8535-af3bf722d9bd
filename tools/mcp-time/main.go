package main

import (
	"context"
	"errors"
	"flag"
	"fmt"
	"log"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
)

var (
	transport    string
	serverlisten string
)

var (
	serverName    = "github.com/songjiayang/current-time"
	serverVersion = "1.0.0"
)

func init() {
	flag.StringVar(&transport, "transport", "sse", "The transport to use, should be \"stdio\" or \"sse\"")
	flag.StringVar(&serverlisten, "server_listen", "***************:8080", "The sse server listen address")
	flag.Parse()
}

func main() {
	// Create MCP server
	s := server.NewMCPServer(
		serverName,
		serverVersion,
	)
	// Add tool
	tool := mcp.NewTool("current time",
		mcp.WithDescription("Get current time with timezone, Asia/Shanghai is default"),
		mcp.WithString("timezone",
			mcp.Required(),
			mcp.Description("current time timezone"),
		),
		// 【新增】添加 "format" 参数
		mcp.WithString("format",
			// 注意：这里没有 mcp.Required()，所以这个参数是可选的
			mcp.Description("Optional. The output format for the time. Can be 'RFC3339', 'Kitchen', or 'UnixDate'. Defaults to RFC1123."),
		),
	)
	// Add tool handler
	s.AddTool(tool, currentTimeHandler)

	// Only check for "sse" since stdio is the default
	if transport == "sse" {
		serverUrl := "http://" + serverlisten
		sseServer := server.NewSSEServer(s, server.WithBaseURL(serverUrl))
		log.Printf("SSE server listening on %s", serverlisten)
		if err := sseServer.Start(serverlisten); err != nil {
			log.Fatalf("Server error: %v", err)
		}
	} else {
		if err := server.ServeStdio(s); err != nil {
			log.Fatalf("Server error: %v", err)
		}
	}
}

func currentTimeHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	timezone, ok := request.Params.Arguments["timezone"].(string)
	if !ok {
		return nil, errors.New("timezone must be a string")
	}

	loc, err := time.LoadLocation(timezone)
	if err != nil {
		return nil, fmt.Errorf("parse timezone with error: %v", err)
	}
	// 2. 获取可选的 "format" 参数
	// 使用 "value, ok" 模式来安全地检查参数是否存在
	format, formatExists := request.Params.Arguments["format"].(string)

	// 如果参数不存在或为空，则使用默认格式
	if !formatExists || format == "" {
		format = time.RFC1123 // 设置默认格式
	}

	// 3. 根据 format 参数格式化时间
	now := time.Now().In(loc)
	var formattedTime string

	// 使用 switch 来处理不同的格式字符串
	switch format {
	case "RFC3339":
		formattedTime = now.Format(time.RFC3339)
	case "Kitchen":
		formattedTime = now.Format(time.Kitchen)
	case "UnixDate":
		formattedTime = now.Format(time.UnixDate)
	case "RFC1123":
		fallthrough // 如果是 "RFC1123" 或其他未识别的格式
	default:
		formattedTime = now.Format(time.RFC1123)
	}

	return mcp.NewToolResultText(fmt.Sprintf(`The current time in %s is: %s`, timezone, formattedTime)), nil
}
