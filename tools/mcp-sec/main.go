package main

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"log"
)

// ThreatIntelReport defines the structure for threat intelligence tool's output.
type ThreatIntelReport struct {
	IPAddress            string `json:"ipAddress"`
	AbuseConfidenceScore int    `json:"abuseConfidenceScore"`
	CountryCode          string `json:"countryCode"`
	ISP                  string `json:"isp"`
	IsProxy              bool   `json:"isProxy"`
}

// IPHistoryRecord defines the structure for a single historical attack record.
type IPHistoryRecord struct {
	Timestamp      string `json:"timestamp"`
	RuleID         string `json:"ruleId"`
	URI            string `json:"uri"`
	ResponseCode   int    `json:"response_code"`
	AttackCategory string `json:"attack_category"`
}

// BlockIPInput defines the input parameters for the block_ip tool.
type BlockIPInput struct {
	IPAddress     string `json:"ip_address"`
	Reason        string `json:"reason"`
	DurationHours int    `json:"duration_hours"`
}

// getThreatIntelHandler is the processor for the get_threat_intel tool.
// It uses the mcp.CallToolRequest and mcp.CallToolResult signature.
func getThreatIntelHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 1. Safely parse parameters from the request.
	ip, ok := request.Params.Arguments["ip_address"].(string)
	if !ok || ip == "" {
		return nil, fmt.Errorf("parameter 'ip_address' is required and must be a string")
	}
	fmt.Printf("✅ Handler [get_threat_intel] CALLED with IP: %s\n", ip)

	// 2. Mock the business logic.
	var report *ThreatIntelReport
	if ip == "**************" {
		report = &ThreatIntelReport{
			IPAddress:            ip,
			AbuseConfidenceScore: 10,
			CountryCode:          "CN",
			ISP:                  "Internal Network",
			IsProxy:              false,
		}
	} else {
		report = &ThreatIntelReport{
			IPAddress:            ip,
			AbuseConfidenceScore: 95,
			CountryCode:          "RU",
			ISP:                  "Known Malicious ISP",
			IsProxy:              true,
		}
	}

	// 3. Serialize the result struct to a JSON string.
	reportBytes, err := json.Marshal(report)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize threat report to JSON: %w", err)
	}

	// 4. Return the JSON string using NewToolResultText, as per the library's API.
	return mcp.NewToolResultText(string(reportBytes)), nil
}

// getIPHistoryHandler is the processor for the get_ip_history tool.
func getIPHistoryHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 1. Parse parameters.
	ip, ok := request.Params.Arguments["ip_address"].(string)
	if !ok || ip == "" {
		return nil, fmt.Errorf("parameter 'ip_address' is required and must be a string")
	}
	fmt.Printf("✅ Handler [get_ip_history] CALLED with IP: %s\n", ip)

	// 2. Mock the business logic.
	var history []IPHistoryRecord
	if ip == "**************" {
		history = []IPHistoryRecord{
			{Timestamp: "2025-08-21T06:46:37Z", RuleID: "931100", URI: "/externalLinks", ResponseCode: 403, AttackCategory: "RFI"},
			{Timestamp: "2025-08-21T06:42:05Z", RuleID: "942100", URI: "/search", ResponseCode: 403, AttackCategory: "SQL Injection"},
		}
	} else {
		history = []IPHistoryRecord{}
	}

	// 3. Serialize the result slice to a JSON string.
	historyBytes, err := json.Marshal(history)
	if err != nil {
		return nil, fmt.Errorf("failed to serialize history to JSON: %w", err)
	}

	// 4. Return the JSON string using NewToolResultText.
	return mcp.NewToolResultText(string(historyBytes)), nil
}

// blockIPHandler demonstrates handling complex inputs and returning plain text.
func blockIPHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 1. Parse complex JSON input from the arguments.
	var input BlockIPInput
	inputBytes, _ := json.Marshal(request.Params.Arguments)
	if err := json.Unmarshal(inputBytes, &input); err != nil {
		return nil, fmt.Errorf("failed to parse arguments for block_ip: %w", err)
	}

	fmt.Printf("✅ Handler [block_ip] CALLED: Blocking IP %s for %d hours. Reason: %s\n", input.IPAddress, input.DurationHours, input.Reason)

	// 2. Mock the action and create a confirmation message.
	resultText := fmt.Sprintf("Action Confirmed: IP address %s has been successfully blocked for %d hours due to: '%s'.", input.IPAddress, input.DurationHours, input.Reason)

	// 3. Return the plain text confirmation.
	return mcp.NewToolResultText(resultText), nil
}

func main() {
	// 1. 创建 MCP 服务器实例
	s := server.NewMCPServer("soc_analyst_tools_standard", "1.0.1")

	// 2. 定义工具
	threatIntelTool := mcp.NewTool(
		"get_threat_intel",
		mcp.WithDescription("查询 IP 的外部威胁情报。"),
		mcp.WithString("ip_address", mcp.Required(), mcp.Description("要查询的 IPv4 地址。")),
	)

	ipHistoryTool := mcp.NewTool(
		"get_ip_history",
		mcp.WithDescription("查询 IP 在系统内部的 WAF 告警历史。"),
		mcp.WithString("ip_address", mcp.Required(), mcp.Description("要查询的 IPv4 地址。")),
	)

	// 添加一个新的动作工具
	blockIPTool := mcp.NewTool(
		"block_ip",
		mcp.WithDescription("在防火墙上封禁一个恶意 IP 地址。这是一个高风险操作。"),
		mcp.WithString("ip_address", mcp.Required(), mcp.Description("要封禁的 IP 地址。")),
		mcp.WithString("reason", mcp.Required(), mcp.Description("封禁的原因，必须清晰明确。")),
		mcp.WithNumber("duration_hours", mcp.Description("封禁时长（小时），默认为 24。")),
	)

	// 3. 添加工具及其 Handler
	s.AddTool(threatIntelTool, getThreatIntelHandler)
	s.AddTool(ipHistoryTool, getIPHistoryHandler)
	s.AddTool(blockIPTool, blockIPHandler) // 添加新的工具和 handler

	// 4. 启动服务器
	listenAddr := "0.0.0.0:8080"
	serverURL := "http://***************:8080" // 使用 127.0.0.1 避免跨域问题
	sseServer := server.NewSSEServer(s, server.WithBaseURL(serverURL))

	log.Printf("MCP SSE 服务器正在启动，监听地址: %s", listenAddr)
	log.Printf("请在您的客户端(如Gemini)中配置工具地址为: %s", serverURL)

	if err := sseServer.Start(listenAddr); err != nil {
		log.Fatalf("服务器错误: %v", err)
	}
}
