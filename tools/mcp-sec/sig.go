package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
)

// --- 1. SigNoz API 响应的结构 ---

type SigNozAPIResponse struct {
	Data SigNozData `json:"data"`
}

type SigNozData struct {
	Result []SigNozResult `json:"result"`
}

type SigNozResult struct {
	List []SigNozListItem `json:"list"`
}

type SigNozListItem struct {
	Timestamp string        `json:"timestamp"`
	Data      SigNozLogData `json:"data"`
}

type SigNozLogData struct {
	Body string `json:"body"` // 我们最关心的字段，内含原始 ModSecurity JSON
}

// --- 2. ModSecurity 原始日志的结构 (与 body JSON 层级 100% 匹配) ---

type Transaction struct {
	ClientIP   string    `json:"client_ip"`
	TimeStamp  string    `json:"time_stamp"`
	ServerID   string    `json:"server_id"`
	ClientPort int       `json:"client_port"`
	HostIP     string    `json:"host_ip"`
	HostPort   int       `json:"host_port"`
	UniqueID   string    `json:"unique_id"`
	Request    Request   `json:"request"`
	Response   Response  `json:"response"`
	Producer   Producer  `json:"producer"`
	Messages   []Message `json:"messages"`
}

type Request struct {
	Method      string            `json:"method"`
	HTTPVersion float64           `json:"http_version"`
	URI         string            `json:"uri"`
	Headers     map[string]string `json:"headers"`
}
type Response struct {
	Body     string            `json:"body"`
	HTTPCode int               `json:"http_code"`
	Headers  map[string]string `json:"headers"`
}
type Producer struct {
	ModSecurity    string   `json:"modsecurity"`
	Connector      string   `json:"connector"`
	SecrulesEngine string   `json:"secrules_engine"`
	Components     []string `json:"components"`
}
type Message struct {
	Message string  `json:"message"`
	Details Details `json:"details"`
}
type Details struct {
	Match  string   `json:"match"`
	RuleID string   `json:"ruleId"`
	Tags   []string `json:"tags"`
}

// --- 3. 我们最终的输出：事件摘要 ---

type IncidentDigest struct {
	Timeline        TimelineSummary `json:"timeline"`
	ActorsSummary   ActorsSummary   `json:"actors_summary"`
	VictimsSummary  VictimsSummary  `json:"victims_summary"`
	BehaviorSummary BehaviorSummary `json:"behavior_summary"`
}

type TimelineSummary struct {
	StartTime        time.Time `json:"start_time"`
	EndTime          time.Time `json:"end_time"`
	DurationSeconds  float64   `json:"duration_seconds"`
	LogCountAnalyzed int       `json:"log_count_analyzed"`
}

type ActorsSummary struct {
	DistinctActorsCount int         `json:"distinct_actors_count"`
	TopActors           []ActorInfo `json:"top_actors"`
}

type ActorInfo struct {
	ActorFingerprint string `json:"actor_fingerprint"`
	LogCount         int    `json:"log_count"`
	IsPrimarySuspect bool   `json:"is_primary_suspect"`
}

type VictimsSummary struct {
	DistinctURIsAttacked int       `json:"distinct_uris_attacked"`
	TopAttackedURIs      []URIInfo `json:"top_attacked_uris"`
	CriticalAssetsHit    []string  `json:"critical_assets_hit"`
}

type URIInfo struct {
	URI   string `json:"uri"`
	Count int    `json:"count"`
}

type BehaviorSummary struct {
	PrimaryAttackType            string         `json:"primary_attack_type"`
	AttackCategoriesDistribution map[string]int `json:"attack_categories_distribution"`
	TopTriggeredRules            []RuleInfo     `json:"top_triggered_rules"`
	HighestAnomalyScore          int            `json:"highest_anomaly_score"`
}

type RuleInfo struct {
	RuleID  string `json:"rule_id"`
	Message string `json:"message"`
	Count   int    `json:"count"`
}

// CreateIncidentDigestFromLogs 接收一个 Transaction 列表并返回摘要
func CreateIncidentDigestFromLogs(transactions []Transaction) (*IncidentDigest, error) {
	// 如果没有日志传入，直接返回错误，避免后续处理出错。
	if len(transactions) == 0 {
		return nil, fmt.Errorf("no transaction logs provided to summarize")
	}

	// 初始化用于聚合数据的临时变量。
	// 使用 map 可以方便地对重复出现的元素进行计数。
	actorCounts := make(map[string]int)     // actor_fingerprint -> 出现次数
	uriCounts := make(map[string]int)       // uri -> 出现次数
	ruleCounts := make(map[string]int)      // rule_id -> 出现次数
	ruleMessages := make(map[string]string) // rule_id -> 规则描述
	categoryCounts := make(map[string]int)  // 攻击类型 -> 出现次数
	highestScore := 0                       // 本次事件中的最高异常分数
	var timestamps []time.Time              // 用于存储所有成功解析的时间戳
	// 遍历从 Signoz 获取的每一条 Transaction 日志。
	for _, tx := range transactions {
		// --- 行为分析部分 ---
		// 无论时间戳是否能解析，都必须先完成这部分的分析。
		// 1. 提取并统计 Actor 指纹（攻击者）。
		actorFingerprint := getActorFingerprint(tx)
		if actorFingerprint != "" {
			actorCounts[actorFingerprint]++
		}
		// 2. 提取并统计被攻击的 URI（受害者）。
		// 我们只关心路径，忽略查询参数。
		uriPath := strings.Split(tx.Request.URI, "?")[0]
		uriCounts[uriPath]++
		// 3. 遍历该条日志中的所有 ModSecurity message，提取规则、攻击类型和分数。
		for _, msg := range tx.Messages {
			if msg.Details.RuleID == "" {
				continue
			}
			// 统计规则ID出现次数，并记录其描述信息。
			ruleCounts[msg.Details.RuleID]++
			if _, ok := ruleMessages[msg.Details.RuleID]; !ok {
				ruleMessages[msg.Details.RuleID] = msg.Message
			}
			// 从规则的 tags 中提取攻击类型（例如 "attack-rce" -> "RCE"）。
			for _, tag := range msg.Details.Tags {
				if strings.HasPrefix(tag, "attack-") {
					category := strings.ToUpper(strings.TrimPrefix(tag, "attack-"))
					categoryCounts[category]++
				}
			}
			// 专门处理计算总分的规则 (ID: 949110)。
			if msg.Details.RuleID == "949110" {
				re := regexp.MustCompile(`\(Value: \` + "`" + `(\d+)'\s*\)`)
				matches := re.FindStringSubmatch(msg.Details.Match)
				// 如果成功匹配并提取出数字...
				if len(matches) > 1 {
					score, _ := strconv.Atoi(matches[1]) // 将提取出的字符串转换为整数
					// 更新事件的最高分记录。
					if score > highestScore {
						highestScore = score
					}
				}
			}
		}
		// --- 时间戳处理部分 ---
		// 独立处理时间戳，即使某条日志的时间戳解析失败，也不会影响上面已完成的行为分析。
		ts, err := time.Parse("Mon Jan 02 15:04:05 2006", tx.TimeStamp)
		if err != nil {
			log.Printf("Warning: could not parse timestamp '%s'. Error: %v", tx.TimeStamp, err)
		} else {
			timestamps = append(timestamps, ts)
		}
	}
	// 在所有日志都处理完毕后，统一计算时间的起止点。
	var firstTimestamp, lastTimestamp time.Time
	if len(timestamps) > 0 {
		sort.Slice(timestamps, func(i, j int) bool { return timestamps[i].Before(timestamps[j]) })
		firstTimestamp = timestamps[0]
		lastTimestamp = timestamps[len(timestamps)-1]
	}
	// 组装最终的摘要对象，供 AI 使用。
	digest := &IncidentDigest{
		Timeline: TimelineSummary{
			StartTime:        firstTimestamp,
			EndTime:          lastTimestamp,
			DurationSeconds:  lastTimestamp.Sub(firstTimestamp).Seconds(),
			LogCountAnalyzed: len(transactions),
		},
		ActorsSummary:   buildActorsSummary(actorCounts),
		VictimsSummary:  buildVictimsSummary(uriCounts),
		BehaviorSummary: buildBehaviorSummary(ruleCounts, ruleMessages, categoryCounts, highestScore),
	}
	return digest, nil
}

// getActorFingerprint 从日志中提取攻击者的唯一标识。
// 优先使用 User-Agent，未来可以扩展为优先使用 Session ID。
func getActorFingerprint(tx Transaction) string {
	if ua, ok := tx.Request.Headers["user-agent"]; ok {
		return ua
	}
	return "unknown_actor"
}

// buildActorsSummary 将聚合的 actor 计数转换为排序后的摘要结构。
func buildActorsSummary(counts map[string]int) ActorsSummary {
	var actors []ActorInfo
	for fingerprint, count := range counts {
		actors = append(actors, ActorInfo{ActorFingerprint: fingerprint, LogCount: count})
	}
	// 按日志数量降序排列，将最活跃的攻击者放在首位。
	sort.Slice(actors, func(i, j int) bool { return actors[i].LogCount > actors[j].LogCount })
	if len(actors) > 0 {
		// 将最活跃的标记为主要嫌疑人。
		actors[0].IsPrimarySuspect = true
	}
	return ActorsSummary{
		DistinctActorsCount: len(counts),
		TopActors:           actors,
	}
}

// buildVictimsSummary 将聚合的 URI 计数转换为排序后的摘要结构。
func buildVictimsSummary(counts map[string]int) VictimsSummary {
	var uris []URIInfo
	for uri, count := range counts {
		uris = append(uris, URIInfo{URI: uri, Count: count})
	}
	sort.Slice(uris, func(i, j int) bool { return uris[i].Count > uris[j].Count })
	// 假设 /api/ 路径下的都是关键资产，进行标记。
	var criticalHits []string
	for _, u := range uris {
		if strings.HasPrefix(u.URI, "/api/") {
			criticalHits = append(criticalHits, u.URI)
		}
	}

	return VictimsSummary{
		DistinctURIsAttacked: len(counts),
		TopAttackedURIs:      uris,
		CriticalAssetsHit:    criticalHits,
	}
}

// buildBehaviorSummary 整合所有与攻击行为相关的数据。
func buildBehaviorSummary(ruleCounts map[string]int, ruleMessages map[string]string, categoryCounts map[string]int, score int) BehaviorSummary {
	var rules []RuleInfo
	for id, count := range ruleCounts {
		rules = append(rules, RuleInfo{RuleID: id, Message: ruleMessages[id], Count: count})
	}
	sort.Slice(rules, func(i, j int) bool { return rules[i].Count > rules[j].Count })
	// 找出本次事件中最主要的攻击类型。
	primaryType := ""
	maxCount := 0
	for cat, count := range categoryCounts {
		if count > maxCount {
			maxCount = count
			primaryType = cat
		}
	}

	return BehaviorSummary{
		PrimaryAttackType:            primaryType,
		AttackCategoriesDistribution: categoryCounts,
		TopTriggeredRules:            rules,
		HighestAnomalyScore:          score,
	}
}

// GetHighSeverityLogsTool 是我们工具的入口函数
func GetHighSeverityLogsTool(rawApiResponse []byte) (*IncidentDigest, error) {
	fmt.Println("--- [TOOL START]: get_high_severity_logs ---")

	// 1. 解析顶层的 SigNoz API 响应
	var apiResponse SigNozAPIResponse
	if err := json.Unmarshal(rawApiResponse, &apiResponse); err != nil {
		return nil, fmt.Errorf("error parsing signoz api response: %w", err)
	}

	// 2. 从响应中提取并解析所有内嵌的 ModSecurity 日志
	var transactions []Transaction
	if len(apiResponse.Data.Result) > 0 {
		for _, item := range apiResponse.Data.Result[0].List {
			// Body 是一个 JSON 字符串，其根是一个名为 "transaction" 的对象
			var bodyWrapper struct {
				Transaction Transaction `json:"transaction"`
			}
			if err := json.Unmarshal([]byte(item.Data.Body), &bodyWrapper); err != nil {
				log.Printf("Warning: Skipping a log due to parsing error in body: %v", err)
				continue
			}
			transactions = append(transactions, bodyWrapper.Transaction)
		}
	}

	fmt.Printf(" INFO: Extracted and successfully parsed %d ModSecurity logs.\n", len(transactions))

	// 3. 调用核心摘要器逻辑
	fmt.Println(" ANALYZING: Creating incident digest from parsed logs...")
	digest, err := CreateIncidentDigestFromLogs(transactions)
	if err != nil {
		return nil, fmt.Errorf("error creating incident digest: %w", err)
	}

	fmt.Println("--- [TOOL END]: Digest created successfully ---")
	return digest, nil
}

func main() {
	// 将您提供的完整 SigNoz 响应 JSON 放在这里
	url := "http://***********:8080/api/v4/query_range"

	payload := strings.NewReader("{\r\n    \"start\": 1756335600000,\r\n    \"end\": 1756344616000,\r\n    \"step\": 60,\r\n    \"variables\": {},\r\n    \"compositeQuery\": {\r\n        \"queryType\": \"builder\",\r\n        \"panelType\": \"list\",\r\n        \"builderQueries\": {\r\n            \"A\": {\r\n                \"dataSource\": \"logs\",\r\n                \"queryName\": \"A\",\r\n                \"aggregateOperator\": \"noop\",\r\n                \"aggregateAttribute\": {},\r\n                \"filters\": {\r\n                    \"items\": [\r\n                        {\r\n                            \"key\": {\r\n                                \"key\": \"http_response\",\r\n                                \"dataType\": \"float64\",\r\n                                \"type\": \"tag\",\r\n                                \"isColumn\": false\r\n                            },\r\n                            \"op\": \"=\",\r\n                            \"value\": 403\r\n                        }\r\n                    ],\r\n                    \"op\": \"AND\"\r\n                },\r\n                \"expression\": \"A\",\r\n                \"disabled\": false,\r\n                \"stepInterval\": 60,\r\n                \"orderBy\": [\r\n                    {\r\n                        \"columnName\": \"timestamp\",\r\n                        \"order\": \"desc\"\r\n                    }\r\n                ],\r\n                \"offset\": 0,\r\n                \"pageSize\": 10\r\n            }\r\n        }\r\n    }\r\n}")

	req, _ := http.NewRequest("POST", url, payload)

	req.Header.Add("SIGNOZ-API-KEY", "/imNEdCHDwAGkspzCl4+mg3VAameA6dmLyj0A01PVLo=")
	req.Header.Add("content-type", "application/json")

	res, _ := http.DefaultClient.Do(req)

	defer res.Body.Close()
	body, _ := ioutil.ReadAll(res.Body)

	fmt.Println(res)
	fmt.Println(string(body))

	// 执行工具
	incidentDigest, err := GetHighSeverityLogsTool(body)
	if err != nil {
		log.Fatalf("Tool execution failed: %v", err)
	}

	// 打印最终的摘要，这就是工具返回给 AI 的内容
	fmt.Println("\n--- FINAL OUTPUT (Returned to AI Agent) ---")
	prettyJSON, _ := json.MarshalIndent(incidentDigest, "", "  ")
	fmt.Println(string(prettyJSON))
}
