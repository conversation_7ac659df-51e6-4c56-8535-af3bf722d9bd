package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/mark3labs/mcp-go/mcp"
	"github.com/mark3labs/mcp-go/server"
	"github.com/xuri/excelize/v2"
)

// assetData 用来在内存中缓存所有Excel文件的数据。
// 键是资产类型(如 "servers")，值是数据本身。
// 数据结构：一个map的切片，每个map代表一行，键是列头，值是单元格内容。
var assetData = make(map[string][]map[string]string)

// loadAssetData 在程序启动时加载所有Excel文件。
func loadAssetData(dir string) error {
	files, err := os.ReadDir(dir)
	if err != nil {
		return fmt.Errorf("无法读取资产目录 '%s': %w", dir, err)
	}

	for _, file := range files {
		if !file.IsDir() && strings.HasSuffix(file.Name(), ".xlsx") {
			filePath := filepath.Join(dir, file.Name())
			assetType := strings.TrimSuffix(file.Name(), ".xlsx")

			f, err := excelize.OpenFile(filePath)
			if err != nil {
				log.Printf("警告：无法打开Excel文件 %s: %v", filePath, err)
				continue
			}
			defer f.Close()

			// 假设第一个sheet是我们要的数据
			sheetName := f.GetSheetName(0)
			rows, err := f.GetRows(sheetName)
			if err != nil {
				log.Printf("警告：无法读取文件 %s 的sheet: %v", filePath, err)
				continue
			}

			if len(rows) < 2 { // 至少需要一个表头行和一行数据
				log.Printf("警告：文件 %s 数据行不足，已跳过", filePath)
				continue
			}

			headers := rows[0]
			var tableData []map[string]string

			for _, row := range rows[1:] {
				rowData := make(map[string]string)
				for i, header := range headers {
					if i < len(row) {
						rowData[header] = row[i]
					} else {
						rowData[header] = "" // 处理行末尾的空单元格
					}
				}
				tableData = append(tableData, rowData)
			}
			assetData[assetType] = tableData
			log.Printf("成功加载资产 '%s' (%d 条记录)", assetType, len(tableData))
		}
	}
	return nil
}

// assetQueryHandler 是处理工具调用的核心函数。
func assetQueryHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	// 1. 解析来自大模型的参数
	assetType, ok := request.Params.Arguments["asset_type"].(string)
	if !ok || assetType == "" {
		return nil, fmt.Errorf("错误: 'asset_type' 参数是必需的且必须是字符串")
	}

	// filters是可选参数
	filtersStr, _ := request.Params.Arguments["filters"].(string)

	// 2. 查找对应的资产数据
	data, exists := assetData[assetType]
	if !exists {
		return mcp.NewToolResultText(fmt.Sprintf("错误: 未找到名为 '%s' 的资产类型。", assetType)), nil
	}

	// 3. 应用过滤器
	var filteredData []map[string]string
	if filtersStr == "" {
		// 如果没有过滤器，返回所有数据
		filteredData = data
	} else {
		// 解析过滤器: "key1:value1,key2:value2"
		filters := make(map[string]string)
		for _, f := range strings.Split(filtersStr, ",") {
			parts := strings.SplitN(f, ":", 2)
			if len(parts) == 2 {
				filters[strings.TrimSpace(parts[0])] = strings.TrimSpace(parts[1])
			}
		}

		// 遍历数据行，寻找匹配项
		for _, row := range data {
			match := true
			for key, value := range filters {
				// 使用 strings.Contains 实现模糊匹配，比完全相等更灵活
				if cellValue, ok := row[key]; !ok || !strings.Contains(strings.ToLower(cellValue), strings.ToLower(value)) {
					match = false
					break
				}
			}
			if match {
				filteredData = append(filteredData, row)
			}
		}
	}

	// 4. 将结果格式化为 Markdown 表格
	if len(filteredData) == 0 {
		return mcp.NewToolResultText("根据您的条件，没有找到任何资产。"), nil
	}

	var sb strings.Builder
	headers := make([]string, 0, len(data[0]))
	for h := range data[0] {
		headers = append(headers, h)
	}

	// 表头
	sb.WriteString("| " + strings.Join(headers, " | ") + " |\n")
	// 分割线
	sb.WriteString("|" + strings.Repeat("---|", len(headers)) + "\n")
	// 数据行
	for _, row := range filteredData {
		var rowValues []string
		for _, h := range headers {
			rowValues = append(rowValues, row[h])
		}
		sb.WriteString("| " + strings.Join(rowValues, " | ") + " |\n")
	}
	return mcp.NewToolResultText(sb.String()), nil
}

func main() {
	// 在启动时加载所有Excel数据
	if err := loadAssetData("/home/<USER>/workspace/enio-demo-project/eino-mcp-main/tools/mcp-assets/assets"); err != nil {
		log.Fatalf("启动失败: 加载资产数据时出错: %v", err)
	}

	if len(assetData) == 0 {
		log.Fatal("启动失败: 未加载任何资产数据，请检查 'assets' 目录和Excel文件。")
	}

	// 创建 MCP 服务器
	s := server.NewMCPServer(
		"asset_manager",
		"1.0.0",
	)

	// 定义工具，这是给大模型看的“说明书”
	assetQueryTool := mcp.NewTool(
		"query_assets",
		mcp.WithDescription("查询公司资产信息，如服务器、笔记本电脑等。"),
		mcp.WithString("asset_type",
			mcp.Required(),
			mcp.Description(fmt.Sprintf("要查询的资产类型。必须是以下之一: %s", getAssetTypes())),
		),
		mcp.WithString("filters",
			// 注意：这个参数是可选的
			mcp.Description("可选的过滤条件，格式为 '列名:值,另一列:另一个值'。例如: 'Owner:Alice,Status:active'"),
		),
	)

	// 将工具和它的处理器函数添加到服务器
	s.AddTool(assetQueryTool, assetQueryHandler)

	// 启动服务器（这里使用SSE，也可以用Stdio）
	listenAddr := "0.0.0.0:8080"
	serverURL := "http://***************:8080" // 使用 127.0.0.1 避免跨域问题
	sseServer := server.NewSSEServer(s, server.WithBaseURL(serverURL))

	log.Printf("MCP SSE 服务器正在启动，监听地址: %s", listenAddr)
	log.Printf("请在您的客户端(如Gemini)中配置工具地址为: %s", serverURL)

	if err := sseServer.Start(listenAddr); err != nil {
		log.Fatalf("服务器错误: %v", err)
	}
}

// getAssetTypes 是一个辅助函数，用于动态生成资产类型列表给大模型看。
func getAssetTypes() string {
	keys := make([]string, 0, len(assetData))
	for k := range assetData {
		keys = append(keys, k)
	}
	return "'" + strings.Join(keys, "', '") + "'"
}
