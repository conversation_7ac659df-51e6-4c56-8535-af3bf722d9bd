package main

import (
	"bufio"
	"context"
	"fmt"
	"github.com/cloudwego/eino-ext/components/model/openai"
	"github.com/cloudwego/eino/compose"
	"log"
	"os"
	"strings"

	eino_mcp "github.com/cloudwego/eino-ext/components/tool/mcp"
	"github.com/cloudwego/eino/flow/agent/react"
	"github.com/cloudwego/eino/schema"
	"github.com/mark3labs/mcp-go/client"
	"github.com/mark3labs/mcp-go/mcp"
)

func main() {
	// 初始化 mcp client
	ctx := context.Background()
	cli, _ := client.NewSSEMCPClient("http://localhost:8080/sse")
	cli.Start(ctx)
	defer cli.Close()

	// 发送 init request
	initRequest := mcp.InitializeRequest{}
	initRequest.Params.ProtocolVersion = mcp.LATEST_PROTOCOL_VERSION
	initRequest.Params.ClientInfo = mcp.Implementation{
		Name:    "current-time",
		Version: "1.0.0",
	}
	initResult, err := cli.Initialize(ctx, initRequest)
	if err != nil {
		log.Fatalf("Failed to initialize: %v", err)
	}
	fmt.Printf(
		"Initialized with server: %s %s\n\n",
		initResult.ServerInfo.Name,
		initResult.ServerInfo.Version,
	)

	// 从服务器获取工具列表
	fmt.Println()
	fmt.Println("可用工具列表:")
	testToolsRequest := mcp.ListToolsRequest{}
	testTools, err := cli.ListTools(ctx, testToolsRequest)
	if err != nil {
		panic(err)
	}

	for _, tool := range testTools.Tools {
		fmt.Printf("- %s: %s\n", tool.Name, tool.Description)
		fmt.Println("参数:", tool.InputSchema.Properties)
	}
	fmt.Println()

	// 调用工具
	fmt.Println("调用工具: timezone")
	toolRequest := mcp.CallToolRequest{
		Request: mcp.Request{
			Method: "tools/call",
		},
	}
	toolRequest.Params.Name = "current time"
	toolRequest.Params.Arguments = map[string]any{
		"timezone": "Asia/Shanghai",
	}
	// Call the tool
	result, err := cli.CallTool(ctx, toolRequest)
	if err != nil {
		panic(err)
	}
	fmt.Println("调用工具结果:", result.Content[0].(mcp.TextContent).Text)

	// 查询 mcp tools
	tools, _ := eino_mcp.GetTools(ctx, &eino_mcp.Config{Cli: cli})

	// mcp tools 与 eino agent 绑定
	llm, _ := openai.NewChatModel(context.Background(), &openai.ChatModelConfig{
		//BaseURL: os.Getenv("OPENAI_API_URL"),
		//Model:   os.Getenv("MODEL_ID"),
		//APIKey:  os.Getenv("OPENAI_API_KEY"),
		BaseURL: "https://open.bigmodel.cn/api/paas/v4/",
		Model:   "GLM-4-Flash",
		APIKey:  "3a6c2a2e684c4aa189ff74b2290e4cac.GAK9WISCyQJAeNji",
	})
	agent, _ := react.NewAgent(ctx, &react.AgentConfig{
		Model:       llm,
		ToolsConfig: compose.ToolsNodeConfig{Tools: tools},
	})

	run(agent)
}

func run(agent *react.Agent) {
	scanner := bufio.NewScanner(os.Stdin)
	fmt.Println("欢迎使用 eino with mcp demo.")
	inputTips := "\n请输入操作: "
	for {
		fmt.Print(inputTips)
		if !scanner.Scan() {
			fmt.Println("读取输入失败，程序退出。")
			return
		}

		input := scanner.Text()

		switch strings.ToLower(input) {
		case "exit", "bye":
			fmt.Println("欢迎再次使用，再见。")
			return
		default:
			output, err := agent.Generate(context.Background(), []*schema.Message{
				{
					Role:    schema.User,
					Content: strings.Replace(input, inputTips, "", 1),
				},
			})
			if err != nil {
				log.Fatalf("run agent with error %v", err)
			}
			fmt.Println(output.Content)
		}
	}
}
